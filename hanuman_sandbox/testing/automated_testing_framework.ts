import { SandboxInfrastructure, SandboxContainer } from '../infrastructure/sandbox_infrastructure';
import { SandboxSecurity } from '../security/sandbox_security';
import { EnvironmentManager, Environment } from '../environments/environment_manager';

/**
 * Framework de Tests Automatisés pour la Sandbox Hanuman
 * Gère les tests unitaires, d'intégration, de régression et de performance
 */

export interface TestCase {
  id: string;
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'regression' | 'performance' | 'security' | 'ui';
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timeout: number;
  retries: number;
  dependencies: string[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  execute: () => Promise<TestResult>;
  expectedResult?: any;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TestResult {
  testId: string;
  status: 'passed' | 'failed' | 'skipped' | 'error';
  duration: number;
  startTime: Date;
  endTime: Date;
  message?: string;
  error?: Error;
  metrics?: {
    performance?: {
      responseTime: number;
      throughput: number;
      memoryUsage: number;
      cpuUsage: number;
    };
    coverage?: {
      lines: number;
      functions: number;
      branches: number;
      statements: number;
    };
    security?: {
      vulnerabilities: number;
      riskLevel: 'low' | 'medium' | 'high' | 'critical';
    };
  };
  artifacts?: {
    logs: string[];
    screenshots?: string[];
    reports?: string[];
  };
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  tests: TestCase[];
  parallel: boolean;
  maxConcurrency: number;
  environment?: string;
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  tags: string[];
}

export interface TestExecution {
  id: string;
  suiteId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    errors: number;
    coverage: number;
    performance: number;
  };
  environment: string;
  triggeredBy: string;
  metadata: {
    branch?: string;
    commit?: string;
    version?: string;
    agent?: string;
    organ?: string;
  };
}

export class AutomatedTestingFramework {
  private infrastructure: SandboxInfrastructure;
  private security: SandboxSecurity;
  private environmentManager: EnvironmentManager;
  private testSuites: Map<string, TestSuite> = new Map();
  private testExecutions: Map<string, TestExecution> = new Map();
  private activeExecutions: Set<string> = new Set();
  private testEnvironments: Map<string, Environment> = new Map();

  constructor(
    infrastructure: SandboxInfrastructure,
    security: SandboxSecurity,
    environmentManager: EnvironmentManager
  ) {
    this.infrastructure = infrastructure;
    this.security = security;
    this.environmentManager = environmentManager;
    this.initializeFramework();
  }

  /**
   * Initialise le framework de tests
   */
  private async initializeFramework(): Promise<void> {
    try {
      // Créer les suites de tests par défaut
      await this.createDefaultTestSuites();

      // Configurer les environnements de test
      await this.setupTestEnvironments();

      console.log('🧪 Framework de Tests Automatisés initialisé');
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du framework de tests:', error);
      throw error;
    }
  }

  /**
   * Crée les suites de tests par défaut
   */
  private async createDefaultTestSuites(): Promise<void> {
    const defaultSuites: TestSuite[] = [
      {
        id: 'infrastructure-tests',
        name: 'Tests Infrastructure',
        description: 'Tests de l\'infrastructure sandbox',
        tests: [],
        parallel: true,
        maxConcurrency: 3,
        tags: ['infrastructure', 'core']
      },
      {
        id: 'security-tests',
        name: 'Tests Sécurité',
        description: 'Tests de sécurité et vulnérabilités',
        tests: [],
        parallel: false,
        maxConcurrency: 1,
        tags: ['security', 'critical']
      },
      {
        id: 'performance-tests',
        name: 'Tests Performance',
        description: 'Tests de performance et charge',
        tests: [],
        parallel: true,
        maxConcurrency: 2,
        tags: ['performance', 'load']
      },
      {
        id: 'integration-tests',
        name: 'Tests Intégration',
        description: 'Tests d\'intégration entre composants',
        tests: [],
        parallel: false,
        maxConcurrency: 1,
        tags: ['integration', 'e2e']
      },
      {
        id: 'regression-tests',
        name: 'Tests Régression',
        description: 'Tests de non-régression',
        tests: [],
        parallel: true,
        maxConcurrency: 4,
        tags: ['regression', 'stability']
      }
    ];

    for (const suite of defaultSuites) {
      this.testSuites.set(suite.id, suite);
    }
  }

  /**
   * Configure les environnements de test
   */
  private async setupTestEnvironments(): Promise<void> {
    const testEnvironmentConfigs = [
      {
        name: 'test-unit',
        type: 'testing' as const,
        config: {
          autoScale: false,
          maxContainers: 2,
          resourceLimits: { cpu: 1, memory: 2048, storage: 5000 },
          securityLevel: 'medium' as const,
          monitoring: { enabled: true, alertThresholds: { cpu: 80, memory: 80, errorRate: 5 } },
          autoCleanup: { enabled: true, inactivityThreshold: 30 }
        }
      },
      {
        name: 'test-integration',
        type: 'testing' as const,
        config: {
          autoScale: true,
          maxContainers: 5,
          resourceLimits: { cpu: 4, memory: 8192, storage: 20000 },
          securityLevel: 'high' as const,
          monitoring: { enabled: true, alertThresholds: { cpu: 70, memory: 70, errorRate: 3 } },
          autoCleanup: { enabled: true, inactivityThreshold: 60 }
        }
      },
      {
        name: 'test-performance',
        type: 'testing' as const,
        config: {
          autoScale: true,
          maxContainers: 10,
          resourceLimits: { cpu: 8, memory: 16384, storage: 50000 },
          securityLevel: 'medium' as const,
          monitoring: { enabled: true, alertThresholds: { cpu: 90, memory: 85, errorRate: 1 } },
          autoCleanup: { enabled: true, inactivityThreshold: 120 }
        }
      }
    ];

    for (const envConfig of testEnvironmentConfigs) {
      try {
        const environment = await this.environmentManager.createEnvironment(envConfig);
        this.testEnvironments.set(environment.id, environment);
        console.log(`✅ Environnement de test créé: ${envConfig.name}`);
      } catch (error) {
        console.error(`❌ Erreur création environnement ${envConfig.name}:`, error);
      }
    }
  }

  /**
   * Ajoute une suite de tests
   */
  async addTestSuite(suite: TestSuite): Promise<void> {
    this.testSuites.set(suite.id, suite);
    console.log(`📋 Suite de tests ajoutée: ${suite.name}`);
  }

  /**
   * Ajoute un test à une suite
   */
  async addTestToSuite(suiteId: string, test: TestCase): Promise<void> {
    const suite = this.testSuites.get(suiteId);
    if (!suite) {
      throw new Error(`Suite de tests non trouvée: ${suiteId}`);
    }

    suite.tests.push(test);
    console.log(`🧪 Test ajouté à la suite ${suite.name}: ${test.name}`);
  }

  /**
   * Exécute une suite de tests
   */
  async executeTestSuite(
    suiteId: string,
    options: {
      environment?: string;
      parallel?: boolean;
      tags?: string[];
      triggeredBy: string;
      metadata?: any;
    }
  ): Promise<TestExecution> {
    const suite = this.testSuites.get(suiteId);
    if (!suite) {
      throw new Error(`Suite de tests non trouvée: ${suiteId}`);
    }

    const execution: TestExecution = {
      id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      suiteId,
      status: 'pending',
      startTime: new Date(),
      results: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        errors: 0,
        coverage: 0,
        performance: 0
      },
      environment: options.environment || 'test-unit',
      triggeredBy: options.triggeredBy,
      metadata: options.metadata || {}
    };

    this.testExecutions.set(execution.id, execution);
    this.activeExecutions.add(execution.id);

    try {
      execution.status = 'running';

      // Filtrer les tests par tags si spécifié
      let testsToRun = suite.tests;
      if (options.tags && options.tags.length > 0) {
        testsToRun = suite.tests.filter(test =>
          test.tags.some(tag => options.tags!.includes(tag))
        );
      }

      execution.summary.total = testsToRun.length;

      // Exécuter les tests
      if (options.parallel && suite.parallel) {
        await this.executeTestsInParallel(testsToRun, execution, suite.maxConcurrency);
      } else {
        await this.executeTestsSequentially(testsToRun, execution);
      }

      execution.status = 'completed';
      execution.endTime = new Date();
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime();

      // Calculer les métriques finales
      this.calculateExecutionSummary(execution);

      console.log(`✅ Exécution de tests terminée: ${execution.id}`);
      console.log(`📊 Résultats: ${execution.summary.passed}/${execution.summary.total} tests réussis`);

    } catch (error) {
      execution.status = 'failed';
      execution.endTime = new Date();
      console.error(`❌ Erreur lors de l'exécution des tests:`, error);
    } finally {
      this.activeExecutions.delete(execution.id);
    }

    return execution;
  }

  /**
   * Exécute les tests en parallèle
   */
  private async executeTestsInParallel(
    tests: TestCase[],
    execution: TestExecution,
    maxConcurrency: number
  ): Promise<void> {
    const chunks = this.chunkArray(tests, maxConcurrency);

    for (const chunk of chunks) {
      const promises = chunk.map(test => this.executeTest(test, execution));
      await Promise.allSettled(promises);
    }
  }

  /**
   * Exécute les tests séquentiellement
   */
  private async executeTestsSequentially(
    tests: TestCase[],
    execution: TestExecution
  ): Promise<void> {
    for (const test of tests) {
      await this.executeTest(test, execution);
    }
  }

  /**
   * Exécute un test individuel
   */
  private async executeTest(test: TestCase, execution: TestExecution): Promise<TestResult> {
    const result: TestResult = {
      testId: test.id,
      status: 'failed',
      duration: 0,
      startTime: new Date(),
      endTime: new Date(),
      artifacts: { logs: [] }
    };

    try {
      console.log(`🧪 Exécution du test: ${test.name}`);

      // Setup du test
      if (test.setup) {
        await test.setup();
      }

      // Exécution avec timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout')), test.timeout);
      });

      const testPromise = test.execute();
      const testResult = await Promise.race([testPromise, timeoutPromise]);

      result.status = 'passed';
      result.message = 'Test réussi';

      // Copier les métriques du résultat du test
      if (testResult.metrics) {
        result.metrics = testResult.metrics;
      }

    } catch (error) {
      result.status = 'failed';
      result.error = error as Error;
      result.message = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error(`❌ Test échoué: ${test.name}`, error);
    } finally {
      // Teardown du test
      try {
        if (test.teardown) {
          await test.teardown();
        }
      } catch (teardownError) {
        console.error(`⚠️ Erreur lors du teardown: ${test.name}`, teardownError);
      }

      result.endTime = new Date();
      result.duration = result.endTime.getTime() - result.startTime.getTime();

      execution.results.push(result);
      this.updateExecutionSummary(execution, result);
    }

    return result;
  }

  /**
   * Met à jour le résumé d'exécution
   */
  private updateExecutionSummary(execution: TestExecution, result: TestResult): void {
    switch (result.status) {
      case 'passed':
        execution.summary.passed++;
        break;
      case 'failed':
        execution.summary.failed++;
        break;
      case 'skipped':
        execution.summary.skipped++;
        break;
      case 'error':
        execution.summary.errors++;
        break;
    }
  }

  /**
   * Calcule le résumé final d'exécution
   */
  private calculateExecutionSummary(execution: TestExecution): void {
    const results = execution.results;

    // Calculer la couverture moyenne
    const coverageResults = results
      .filter(r => r.metrics?.coverage)
      .map(r => r.metrics!.coverage!);

    if (coverageResults.length > 0) {
      const avgCoverage = coverageResults.reduce((sum, cov) =>
        sum + (cov.lines + cov.functions + cov.branches + cov.statements) / 4, 0
      ) / coverageResults.length;
      execution.summary.coverage = Math.round(avgCoverage);
    }

    // Calculer le score de performance moyen
    const performanceResults = results
      .filter(r => r.metrics?.performance)
      .map(r => r.metrics!.performance!);

    if (performanceResults.length > 0) {
      const avgPerformance = performanceResults.reduce((sum, perf) => {
        // Score basé sur le temps de réponse (plus bas = meilleur)
        const responseScore = Math.max(0, 100 - perf.responseTime);
        return sum + responseScore;
      }, 0) / performanceResults.length;
      execution.summary.performance = Math.round(avgPerformance);
    }
  }

  /**
   * Divise un tableau en chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Obtient les résultats d'une exécution
   */
  getExecutionResults(executionId: string): TestExecution | undefined {
    return this.testExecutions.get(executionId);
  }

  /**
   * Obtient toutes les suites de tests
   */
  getTestSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  /**
   * Obtient une suite de tests par ID
   */
  getTestSuite(suiteId: string): TestSuite | undefined {
    return this.testSuites.get(suiteId);
  }

  /**
   * Obtient les exécutions actives
   */
  getActiveExecutions(): TestExecution[] {
    return Array.from(this.activeExecutions)
      .map(id => this.testExecutions.get(id))
      .filter(exec => exec !== undefined) as TestExecution[];
  }

  /**
   * Obtient l'historique des exécutions
   */
  getExecutionHistory(limit: number = 50): TestExecution[] {
    return Array.from(this.testExecutions.values())
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Annule une exécution en cours
   */
  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.testExecutions.get(executionId);
    if (!execution || !this.activeExecutions.has(executionId)) {
      throw new Error(`Exécution non trouvée ou non active: ${executionId}`);
    }

    execution.status = 'cancelled';
    execution.endTime = new Date();
    this.activeExecutions.delete(executionId);

    console.log(`🛑 Exécution annulée: ${executionId}`);
  }

  /**
   * Nettoie les anciens résultats
   */
  async cleanupOldResults(retentionDays: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const toDelete: string[] = [];

    for (const [id, execution] of this.testExecutions) {
      if (execution.startTime < cutoffDate && !this.activeExecutions.has(id)) {
        toDelete.push(id);
      }
    }

    for (const id of toDelete) {
      this.testExecutions.delete(id);
    }

    console.log(`🧹 Nettoyage terminé: ${toDelete.length} exécutions supprimées`);
  }

  /**
   * Génère un rapport de tests
   */
  generateTestReport(executionId: string): {
    execution: TestExecution;
    summary: string;
    details: string;
    recommendations: string[];
  } {
    const execution = this.testExecutions.get(executionId);
    if (!execution) {
      throw new Error(`Exécution non trouvée: ${executionId}`);
    }

    const summary = `
📊 RAPPORT DE TESTS - ${execution.id}
Suite: ${execution.suiteId}
Durée: ${execution.duration ? Math.round(execution.duration / 1000) : 0}s
Résultats: ${execution.summary.passed}/${execution.summary.total} réussis
Couverture: ${execution.summary.coverage}%
Performance: ${execution.summary.performance}%
    `.trim();

    const details = execution.results.map(result => `
🧪 ${result.testId}: ${result.status.toUpperCase()}
   Durée: ${result.duration}ms
   ${result.message || ''}
   ${result.error ? `Erreur: ${result.error.message}` : ''}
    `).join('\n');

    const recommendations: string[] = [];

    if (execution.summary.coverage < 80) {
      recommendations.push('Améliorer la couverture de tests (objectif: 80%+)');
    }

    if (execution.summary.performance < 70) {
      recommendations.push('Optimiser les performances des tests');
    }

    if (execution.summary.failed > 0) {
      recommendations.push('Corriger les tests en échec');
    }

    return {
      execution,
      summary,
      details,
      recommendations
    };
  }

  /**
   * Obtient les exécutions récentes
   */
  async getRecentExecutions(projectId: string, limit: number = 10): Promise<TestExecution[]> {
    // Filtrer les exécutions par projet et retourner les plus récentes
    const allExecutions = Array.from(this.testExecutions.values())
      .filter(exec => exec.metadata.agent === projectId || exec.metadata.organ === projectId)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);

    return allExecutions;
  }

  /**
   * Obtient une exécution par ID
   */
  getExecution(executionId: string): TestExecution | undefined {
    return this.testExecutions.get(executionId);
  }

  /**
   * Obtient toutes les suites de tests
   */
  getTestSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  /**
   * Obtient une suite de tests par ID
   */
  getTestSuite(suiteId: string): TestSuite | undefined {
    return this.testSuites.get(suiteId);
  }

  /**
   * Annule une exécution en cours
   */
  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.testExecutions.get(executionId);
    if (execution && execution.status === 'running') {
      execution.status = 'cancelled';
      execution.endTime = new Date();
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime();
      this.activeExecutions.delete(executionId);
      console.log(`🚫 Exécution annulée: ${executionId}`);
    }
  }

  /**
   * Obtient les statistiques globales
   */
  getGlobalStats(): {
    totalExecutions: number;
    successRate: number;
    averageDuration: number;
    totalTests: number;
    averageCoverage: number;
  } {
    const executions = Array.from(this.testExecutions.values());
    const completedExecutions = executions.filter(e => e.status === 'completed');

    const totalExecutions = executions.length;
    const successRate = totalExecutions > 0 ? (completedExecutions.length / totalExecutions) * 100 : 0;

    const totalDuration = completedExecutions.reduce((sum, e) => sum + (e.duration || 0), 0);
    const averageDuration = completedExecutions.length > 0 ? totalDuration / completedExecutions.length : 0;

    const totalTests = executions.reduce((sum, e) => sum + e.summary.total, 0);
    const totalCoverage = executions.reduce((sum, e) => sum + e.summary.coverage, 0);
    const averageCoverage = executions.length > 0 ? totalCoverage / executions.length : 0;

    return {
      totalExecutions,
      successRate,
      averageDuration,
      totalTests,
      averageCoverage
    };
  }
}
